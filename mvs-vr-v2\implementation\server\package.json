{"name": "mvs-vr-server", "version": "1.0.0", "type": "module", "scripts": {"test": "vitest", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:visual-editors": "vitest run tests/unit/visual-editors", "test:report": "vitest run --coverage && node tests/generate-test-report.js", "test:advanced": "vitest run tests/realtime tests/security tests/monitoring tests/ml", "test:e2e": "playwright test", "test:performance": "echo 'Performance tests not yet configured'", "start:advanced": "node scripts/init-advanced-features.js", "start:services": "docker-compose -f docker-compose.yml -f docker-compose.advanced.yml up -d", "stop:services": "docker-compose -f docker-compose.yml -f docker-compose.advanced.yml down", "monitor:logs": "docker-compose logs -f", "monitor:metrics": "echo 'Open http://localhost:9090'", "monitor:dashboard": "echo 'Open http://localhost:3001'"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "@testing-library/dom": "^9.3.1", "cors": "^2.8.5", "dotenv": "^16.0.3", "expect": "^29.5.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "helmet": "^7.2.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "pino": "^8.16.2", "qrcode": "^1.5.4", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "speakeasy": "^2.0.0", "vitest": "^1.0.0", "ws": "^8.18.2", "zod": "^3.22.4", "@opentelemetry/api": "^1.7.0", "@opentelemetry/sdk-node": "^0.45.1", "@opentelemetry/auto-instrumentations-node": "^0.40.3", "@opentelemetry/exporter-jaeger": "^1.18.1", "@opentelemetry/exporter-prometheus": "^0.45.1", "@opentelemetry/sdk-metrics": "^1.18.1", "@opentelemetry/resources": "^1.18.1", "@opentelemetry/semantic-conventions": "^1.18.1", "prom-client": "^15.1.0"}, "devDependencies": {"@google-cloud/storage": "^7.7.0", "@playwright/test": "^1.40.1", "@testing-library/jest-dom": "^6.6.3", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.2.5", "@types/sinon": "^17.0.2", "@types/supertest": "^2.0.16", "@types/testing-library__jest-dom": "^5.14.6", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "@vitejs/plugin-vue": "^4.5.2", "@vitest/coverage-v8": "^1.6.1", "@vue/test-utils": "^2.4.6", "chai": "^4.3.10", "eslint": "^8.41.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest-dom": "^4.0.3", "eslint-plugin-testing-library": "^5.11.0", "eslint-plugin-vitest": "^0.2.2", "happy-dom": "^12.10.3", "jsdom": "^26.1.0", "sinon": "^17.0.1", "supertest": "^6.3.3", "typescript": "^5.0.4", "vite": "^4.3.9", "vue": "^3.5.14"}}